[package]
authors = ["<PERSON>we<PERSON>ek <<EMAIL>>"]
categories = ["command-line-utilities"]
description = "A simple CLI for subscribing to Youtube RSS feeds without a Youtube account."
edition = "2021"
license = "MIT"
name = "yt-sub"
readme = "../README.md"
repository = "https://github.com/pawurb/yt-sub-rs"
version = "0.2.2"

[profile.release]
lto = true

[[bin]]
name = "ytsub"
path = "bin/main.rs"

[dependencies]
chrono = { version = "0.4.38", features = ["serde"] }
clap = { version = "4.5.20", features = ["derive"] }
env_logger = "0.11.5"
eyre = "0.6.12"
home = "0.5.9"
log = "0.4.22"
reqwest = { version = "0.12", features = ["json"] }
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
tokio = { version = "1.40", features = ["full"] }
toml = "0.8.19"
xmltojson = "0.1.3"
uuid = { version = "1.10.0", features = ["v4"] }
yt-sub-core = "0.2.2"
# yt-sub-core = { path = "../core" }

[dev-dependencies]
mockito = "1.5.0"
