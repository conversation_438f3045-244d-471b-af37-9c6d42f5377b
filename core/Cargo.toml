[package]
name = "yt-sub-core"
version = "0.2.2"
edition = "2021"
categories = ["command-line-utilities"]
description = "yt-sub core library"
license = "MIT"
readme = "../README.md"

[profile.release]
lto = true

[dependencies]
chrono = { version = "0.4.38", features = ["serde"] }
env_logger = "0.11.5"
eyre = "0.6.12"
log = "0.4.22"
reqwest = { version = "0.12", features = ["json"] }
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
toml = "0.8.19"
xmltojson = "0.1.3"

[dev-dependencies]
mockito = "1.5.0"
tokio = { version = "1.40", features = ["full"] }
