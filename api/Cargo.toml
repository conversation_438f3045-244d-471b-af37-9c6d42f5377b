[package]
license = "MIT"
name = "yt-sub-api"
version = "0.0.0"
publish = false
edition = "2021"
authors = ["pawurb <<EMAIL>>"]

[[bin]]
name = "server"
path = "bin/server.rs"

[[bin]]
name = "scheduler"
path = "bin/scheduler.rs"

[package.metadata.release]
release = false

[profile.release]
lto = true

[dependencies]
serde_json = "1.0.128"
yt-sub-core = "0.2.2"
# yt-sub-core = { path = "../core" }
eyre = "0.6.12"
uuid = { version = "1.11.0", features = ["v4", "js"] }
chrono = "0.4.38"
openssl = { version = "0.10", features = ["vendored"] }
axum = "0.7.7"
sqlx = { version = "0.8.2", features = [
  "sqlite",
  "chrono",
  "runtime-tokio-rustls",
] }
reqwest = "0.12"
tokio = { version = "1.40", features = ["full"] }
serde = "1.0.214"
tracing-subscriber = { version = "0.3.18", features = ["time"] }
tracing = "0.1.40"
tower-http = { version = "0.6.1", features = [
  "trace",
  "timeout",
  "compression-gzip",
  "compression-br",
  "compression-deflate",
  "catch-panic",
] }
tower = "0.5.1"
futures = "0.3.31"
tokio-cron-scheduler = { version = "0.13.0", features = ["signal", "english"] }
tracing-appender = "0.2.3"
time = "0.3.36"

[dev-dependencies]
glob = "0.3.1"
reqwest = { version = "0.12", features = ["json"] }
mockito = "1.5.0"
http-body-util = "0.1.2"
