name: Rust CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    name: test ${{ matrix.rust }} 
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        rust: ["stable", "beta", "nightly"]
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - name: Set up Rust
        run: | 
          rustup update ${{ matrix.rust }} 
          rustup default ${{ matrix.rust }}
          rustup component add rustfmt
          rustup component add clippy
      - uses: Swatinem/rust-cache@v2
      - name: Check 'core'
        run: | 
          cd core
          cargo check 
      - name: Fmt 'core'
        run: |
          cd core
          cargo fmt --all --check
      - name: Lint 'core'
        run: |
          cd core
          cargo clippy --all --all-features -- -D warnings
      - name: Run tests 'core'
        run: | 
          cd core
          cargo test
      - name: Check 'cli'
        run: | 
          cd cli
          cargo check 
      - name: Fmt 'cli'
        run: |
          cd cli
          cargo fmt --all --check
      - name: Lint 'cli'
        run: |
          cd cli
          cargo clippy --all --all-features -- -D warnings
      - name: Run tests 'cli'
        run: | 
          cd cli
          cargo test
      - name: Check 'api'
        run: | 
          cd api
          cargo check 
      - name: Fmt 'api'
        run: |
          cd api
          cargo fmt --all --check
      - name: Lint 'api'
        run: |
          cd api
          cargo clippy --all --all-features -- -D warnings
      - name: Run tests 'api'
        run: | 
          cd api
          cargo test